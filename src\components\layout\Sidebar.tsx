"use client";
import React, { useState } from "react";
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { MoreVertical, MessageCircle, Database, Plus, BarChart3, MessageCirclePlus, LayoutDashboard } from 'lucide-react';
import { usePathname, useRouter } from 'next/navigation';
import { useChatHistory } from "@/providers/ChatHistoryContext";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu";

interface SidebarProps {
  onNewChat: () => void;
  onToggleCollapse?: (collapsed: boolean) => void;
  isCreatingNewChat?: boolean;
}

const Sidebar: React.FC<SidebarProps> = ({ onNewChat, onToggleCollapse, isCreatingNewChat = false }) => {
  const { 
    chatHistory, 
    isLoadingChats, 
    deleteChat,
    renameChat, 
  } = useChatHistory();
  const pathname = usePathname();
  const router = useRouter();
  const [menuOpenId, setMenuOpenId] = useState<string | null>(null);
  const [renameId, setRenameId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState<string>("");
  const [isCollapsed, setIsCollapsed] = useState<boolean>(false);

  console.log('Sidebar component rendered. Current pathname:', pathname);

  // Extract chatId from pathname (e.g., /chat/123)
  const currentChatId = pathname?.split('/').includes('chat') ? pathname.split('/').pop() : null;
  console.log('Current chatId extracted from pathname:', currentChatId);

  const handleRename = (id: string, currentTitle: string) => {
    console.log('Renaming chat with id:', id, 'and current title:', currentTitle);
    setRenameId(id);
    setRenameValue(currentTitle);
    setMenuOpenId(null);
  };

  const handleRenameSubmit = (id: string) => {
    console.log('Submitting rename for chat with id:', id, 'and new title:', renameValue);
    if (renameValue.trim()) {
      renameChat(id, renameValue.trim());
    }
    setRenameId(null);
    setRenameValue("");
  };

  const handleDelete = async (chatId: string) => {
    try {
      await deleteChat(chatId);
      setMenuOpenId(null);

      // 🚚 After successful deletion, handle navigation if the deleted chat was active
      if (currentChatId === chatId) {
        // Get the updated chat list (the state will have been updated by deleteChat)
        const remainingChats = chatHistory.filter(chat => chat.id !== chatId);

        if (remainingChats.length > 0) {
          // Navigate to the most recently updated chat (first in list)
          router.push(`/chat/${remainingChats[0].id}`);
        } else {
          // No chats left – start a fresh chat
          onNewChat();
          // Fallback navigate to generic chat route to trigger new-chat UI
          router.push('/chat');
        }
      }
    } catch (error) {
      console.error('Failed to delete chat:', error);
      // You could add a toast notification here if you have one
    }
  };

  const formatTimestamp = (timestamp: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - timestamp.getTime();
    const diffHours = diffMs / (1000 * 60 * 60);
    const diffDays = diffMs / (1000 * 60 * 60 * 24);

    if (diffHours < 1) {
      return 'Just now';
    } else if (diffHours < 24) {
      return `${Math.floor(diffHours)}h ago`;
    } else if (diffDays < 7) {
      return `${Math.floor(diffDays)}d ago`;
    } else {
      return timestamp.toLocaleDateString();
    }
  };

  const handleToggleCollapse = () => {
    const newCollapsedState = !isCollapsed;
    setIsCollapsed(newCollapsedState);
    onToggleCollapse?.(newCollapsedState);
  };

  return (
    <aside 
      className={`hidden lg:flex flex-col ${isCollapsed ? 'w-16' : 'w-sidebar'} h-full min-h-0 transition-all duration-300 flex-shrink-0 relative overflow-y-auto`}
      style={{
        backgroundColor: 'var(--sidebar-bg)',
        borderRight: '1px solid var(--sidebar-border)',
        scrollbarColor: 'var(--sidebar-surface-tertiary) transparent'
      }}
    >
      {/* Toggle Button */}
      <div className={`sticky top-0 z-10 p-2 transition-all duration-300 ease-in-out ${isCollapsed ? 'flex justify-center' : 'flex justify-end'}`}
        style={{ backgroundColor: 'var(--sidebar-bg)' }}
      >
        <Button 
          variant="ghost" 
          size="icon"
          className="w-10 h-10 flex items-center justify-center rounded-lg border-0 transition-all duration-300 ease-in-out transform-gpu"
          style={{ 
            color: 'var(--sidebar-icon) !important',
            backgroundColor: 'transparent !important'
          }}
          onMouseEnter={(e) => {
            e.currentTarget.style.setProperty('background-color', 'var(--interactive-bg-secondary-hover)', 'important');
            e.currentTarget.style.transform = 'scale(1.05)';
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.setProperty('background-color', 'transparent', 'important');
            e.currentTarget.style.transform = 'scale(1)';
          }}
          onClick={handleToggleCollapse}
        >
          <svg 
            className="h-5 w-5 transition-transform duration-300 ease-in-out" 
            viewBox="0 0 20 20" 
            fill="currentColor" 
            xmlns="http://www.w3.org/2000/svg"
            style={{
              transform: isCollapsed ? 'scaleX(-1)' : 'scaleX(1)'
            }}
          >
            <path d="M6.83496 3.99992C6.38353 4.00411 6.01421 4.0122 5.69824 4.03801C5.31232 4.06954 5.03904 4.12266 4.82227 4.20012L4.62207 4.28606C4.18264 4.50996 3.81498 4.85035 3.55859 5.26848L3.45605 5.45207C3.33013 5.69922 3.25006 6.01354 3.20801 6.52824C3.16533 7.05065 3.16504 7.71885 3.16504 8.66301V11.3271C3.16504 12.2712 3.16533 12.9394 3.20801 13.4618C3.25006 13.9766 3.33013 14.2909 3.45605 14.538L3.55859 14.7216C3.81498 15.1397 4.18266 15.4801 4.62207 15.704L4.82227 15.79C5.03904 15.8674 5.31234 15.9205 5.69824 15.9521C6.01398 15.9779 6.383 15.986 6.83398 15.9902L6.83496 3.99992ZM18.165 11.3271C18.165 12.2493 18.1653 12.9811 18.1172 13.5702C18.0745 14.0924 17.9916 14.5472 17.8125 14.9648L17.7295 15.1415C17.394 15.8 16.8834 16.3511 16.2568 16.7353L15.9814 16.8896C15.5157 17.1268 15.0069 17.2285 14.4102 17.2773C13.821 17.3254 13.0893 17.3251 12.167 17.3251H7.83301C6.91071 17.3251 6.17898 17.3254 5.58984 17.2773C5.06757 17.2346 4.61294 17.1508 4.19531 16.9716L4.01855 16.8896C3.36014 16.5541 2.80898 16.0434 2.4248 15.4169L2.27051 15.1415C2.03328 14.6758 1.93158 14.167 1.88281 13.5702C1.83468 12.9811 1.83496 12.2493 1.83496 11.3271V8.66301C1.83496 7.74072 1.83468 7.00898 1.88281 6.41985C1.93157 5.82309 2.03329 5.31432 2.27051 4.84856L2.4248 4.57317C2.80898 3.94666 3.36012 3.436 4.01855 3.10051L4.19531 3.0175C4.61285 2.83843 5.06771 2.75548 5.58984 2.71281C6.17898 2.66468 6.91071 2.66496 7.83301 2.66496H12.17C13.0893 2.66496 13.821 2.66468 14.4102 2.71281C15.0069 2.76157 15.5157 2.86329 15.9814 3.10051L16.2568 3.25481C16.8833 3.63898 17.394 4.19012 17.7295 4.84856L17.8125 5.02531C17.9916 5.44285 18.0745 5.89771 18.1172 6.41985C18.1653 7.00898 18.165 7.74072 18.165 8.66301V11.3271ZM8.16406 15.995H12.167C13.1112 15.995 13.7794 15.9947 14.3018 15.9521C14.8164 15.91 15.1308 15.8299 15.3779 15.704L15.5615 15.6015C15.9797 15.3451 16.32 14.9774 16.5439 14.538L16.6299 14.3378C16.7074 14.121 16.7605 13.8478 16.792 13.4618C16.8347 12.9394 16.835 12.2712 16.835 11.3271V8.66301C16.835 7.71885 16.8347 7.05065 16.792 6.52824C16.7605 6.14232 16.7073 5.86904 16.6299 5.65227L16.5439 5.45207C16.32 5.01264 15.9796 4.64498 15.5615 4.3886L15.3779 4.28606C15.1308 4.16013 14.8165 4.08006 14.3018 4.03801C13.7794 3.99533 13.1112 3.99504 12.167 3.99504H8.16406C8.16407 3.99667 8.16504 3.99829 8.16504 3.99992L8.16406 15.995Z"></path>
          </svg>
        </Button>
      </div>
      
      {!isCollapsed && (
        <div className="flex flex-col h-full px-3">
          {/* Navigation Links */}
          <div className="space-y-1 mb-4">
            <Link href="/dashboard">
              <Button 
                variant="ghost" 
                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/dashboard' ? '' : ''}`}
                style={{
                  color: pathname === '/dashboard' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',
                  backgroundColor: pathname === '/dashboard' ? 'var(--surface-selected)' : 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (pathname !== '/dashboard') {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (pathname !== '/dashboard') {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <LayoutDashboard className="h-4 w-4" style={{ color: 'var(--sidebar-icon)' }} />
                Dashboard
              </Button>
            </Link>
            
            <Link href="/reports">
              <Button 
                variant="ghost" 
                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10 ${pathname === '/reports' ? '' : ''}`}
                style={{
                  color: pathname === '/reports' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',
                  backgroundColor: pathname === '/reports' ? 'var(--surface-selected)' : 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (pathname !== '/reports') {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (pathname !== '/reports') {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <BarChart3 className="h-4 w-4" style={{ color: 'var(--sidebar-icon)' }} />
                Reports
              </Button>
            </Link>
            
            <Link href="/datasources">
              <Button 
                variant="ghost" 
                className={`w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10`}
                style={{
                  color: pathname === '/datasources' ? 'var(--sidebar-text-primary)' : 'var(--sidebar-text-secondary)',
                  backgroundColor: pathname === '/datasources' ? 'var(--surface-selected)' : 'transparent'
                }}
                onMouseEnter={(e) => {
                  if (pathname !== '/datasources') {
                    e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (pathname !== '/datasources') {
                    e.currentTarget.style.backgroundColor = 'transparent';
                  }
                }}
              >
                <Database className="h-4 w-4" style={{ color: 'var(--sidebar-icon)' }} />
                Data Sources
              </Button>
            </Link>
          </div>
          
          {/* New Chat Button */}
          <div className="mb-4">
            <Button 
              variant="ghost" 
              className="w-full justify-start gap-3 text-sm font-normal border-0 rounded-lg transition-all duration-200 h-10" 
              onClick={onNewChat}
              disabled={isCreatingNewChat}
              style={{
                color: 'var(--sidebar-text-secondary)',
                backgroundColor: 'transparent'
              }}
              onMouseEnter={(e) => {
                if (!isCreatingNewChat) {
                  e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                }
              }}
              onMouseLeave={(e) => {
                if (!isCreatingNewChat) {
                  e.currentTarget.style.backgroundColor = 'transparent';
                }
              }}
            >
              <Plus className="h-4 w-4" style={{ color: 'var(--sidebar-icon)' }} />
              {isCreatingNewChat ? 'Creating...' : 'New Chat'}
            </Button>
          </div>
          
          {/* Chat History Section */}
          <div className="flex flex-col gap-1 overflow-y-auto flex-1 pb-4">
            <h3 
              className="text-xs font-medium uppercase tracking-wider px-2 py-2 sticky top-0 z-10"
              style={{ 
                color: 'var(--sidebar-text-tertiary)',
                backgroundColor: 'var(--sidebar-bg)'
              }}
            >
              Chat History
            </h3>
            
            {isLoadingChats && (
              <div className="flex items-center justify-center py-8">
                <div className="text-xs" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                  Loading chats...
                </div>
              </div>
            )}
            
            {!isLoadingChats && chatHistory.map((chat) => {
              const isActive = chat.id === currentChatId;
              const isRenaming = renameId === chat.id;
              return (
                <div
                  key={chat.id}
                  className={`group flex items-center justify-between py-2 px-2 rounded-lg cursor-pointer transition-all duration-200 ${isActive ? '' : ''}`}
                  style={{
                    backgroundColor: isActive ? 'var(--surface-selected)' : 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                    }
                  }}
                  onMouseLeave={(e) => {
                    if (!isActive) {
                      e.currentTarget.style.backgroundColor = 'transparent';
                    }
                  }}
                  onClick={() => router.push(`/chat/${chat.id}`)}
                >
                  {isRenaming ? (
                    <form
                      onSubmit={e => { e.preventDefault(); handleRenameSubmit(chat.id); }}
                      className="flex items-center gap-2 w-full"
                    >
                      <input
                        autoFocus
                        className="truncate max-w-[120px] text-sm font-normal bg-transparent border-b border-blue-400 outline-none px-1"
                        style={{ color: 'var(--sidebar-text-primary)' }}
                        value={renameValue}
                        onChange={e => setRenameValue(e.target.value)}
                        onKeyDown={e => { if (e.key === 'Escape') setRenameId(null); }}
                      />
                      <Button 
                        type="submit" 
                        size="sm" 
                        variant="ghost" 
                        className="text-blue-500 px-2 text-xs rounded border-0"
                      >
                        Save
                      </Button>
                    </form>
                  ) : (
                    <>
                      <div className="flex-1 min-w-0">
                        <span 
                          className="truncate text-sm font-normal block"
                          style={{ color: 'var(--sidebar-text-primary)' }}
                        >
                          {chat.title}
                        </span>
                      </div>
                      
                      <div className="relative ml-2 flex-shrink-0" onClick={e => e.stopPropagation()}>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              type="button"
                              size="icon"
                              variant="ghost"
                              className="w-7 h-7 p-0 opacity-0 group-hover:opacity-100 transition-all duration-200 rounded border-0"
                              style={{ color: 'var(--sidebar-icon)' }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                              }}
                              aria-label="More actions"
                            >
                              <MoreVertical className="w-4 h-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent 
                            align="start" 
                            side="bottom" 
                            sideOffset={8} 
                            className="border-none shadow-xl rounded-xl p-2"
                            style={{
                              backgroundColor: 'var(--sidebar-surface-secondary)',
                              color: 'var(--sidebar-text-primary)'
                            }}
                          >
                            <DropdownMenuItem
                              onClick={() => handleRename(chat.id, chat.title)}
                              className="rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200"
                              style={{ 
                                color: 'var(--sidebar-text-primary)',
                                backgroundColor: 'transparent'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = 'var(--interactive-bg-secondary-hover)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                              }}
                            >
                              Rename
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleDelete(chat.id)}
                              className="rounded-lg px-3 py-2 text-sm font-medium cursor-pointer transition-colors duration-200"
                              style={{ 
                                color: '#ff8583',
                                backgroundColor: 'transparent'
                              }}
                              onMouseEnter={(e) => {
                                e.currentTarget.style.backgroundColor = 'rgba(255, 133, 131, 0.1)';
                              }}
                              onMouseLeave={(e) => {
                                e.currentTarget.style.backgroundColor = 'transparent';
                              }}
                            >
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </>
                  )}
                </div>
              );
            })}
            
            {!isLoadingChats && chatHistory.length === 0 && (
              <div className="text-center py-8">
                <MessageCirclePlus 
                  className="h-12 w-12 mx-auto mb-3" 
                  style={{ color: 'var(--sidebar-text-tertiary)' }}
                />
                <p className="text-xs" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                  No chats yet
                </p>
                <p className="text-xs mt-1" style={{ color: 'var(--sidebar-text-tertiary)' }}>
                  Start a new conversation
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </aside>
  );
};

export default Sidebar;