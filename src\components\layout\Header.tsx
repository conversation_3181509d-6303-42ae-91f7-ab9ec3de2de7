"use client";
import React from 'react';
import Link from 'next/link';
import { But<PERSON> } from "@/components/ui/button";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { Menu, LogOut, User, Settings } from 'lucide-react';
import { useAuth } from '@/providers/AuthContext';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useTheme } from "@/providers/theme-provider";
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { usePageHeader } from '@/providers/PageHeaderContext';

const Header = () => {
  const { isAuthenticated, logout, user } = useAuth();
  const { theme, setTheme } = useTheme();
  const { pageInfo } = usePageHeader();
  
  // Log current theme to ensure it's working
  React.useEffect(() => {
    console.log('Current theme:', theme);
  }, [theme]);

  return (
    <header className="flex items-center justify-between whitespace-nowrap border-b border-solid border-sidebar-border bg-sidebar-bg px-6 py-3 h-16">
      <div className="flex items-center gap-4 text-sidebar-text-primary">
        {isAuthenticated && (
          <div className="lg:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="outline" size="icon">
                  <Menu className="h-6 w-6" />
                </Button>
              </SheetTrigger>
            </Sheet>
          </div>
        )}
        
        {/* Clean Page Title Only - No Icons, No Breadcrumbs */}
        <h1 className="text-lg font-semibold leading-tight tracking-[-0.015em] text-sidebar-text-primary">
          {pageInfo.title}
        </h1>
      </div>
      <div className="flex items-center gap-4">
        <ThemeToggle/>
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div
              className="bg-center bg-no-repeat aspect-square bg-cover rounded-full size-10 border border-gray-300 dark:border-gray-600 cursor-pointer"
              style={{backgroundImage: 'url("https://lh3.googleusercontent.com/aida-public/AB6AXuDWebPngbfq187WqfXE_MtPk8M0EV0xFRjRBo-PyGT3gc5_fe21XmxRmatJb_DA3Uj39Cq6JhWTlbbtn2KqtOd6RUjyzvWzVve175HopQUBswy7d9ghg2PS1cO9xJeZ1ftJx2TtunIZ1x-sGxGrf0bW0QYQntujt7Y8sa5M0arS4GPA-iWzr78ev9XXvm38XfzADeYDZbnx6XfjlmdWxxQ3PIz2Yi_OEZSrEL1Qqt1wVSgyOGLDV7dr1DLDUgSABu8zXbfuhsM0M4Y")'}}
            />
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-md rounded-md">
            <DropdownMenuItem asChild>
              <Link href="/profile" className="flex items-center gap-2 text-gray-900 dark:text-white">
                <User className="w-4 h-4" />
                Profile
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem asChild>
              <Link href="/settings" className="flex items-center gap-2 text-gray-900 dark:text-white">
                <Settings className="w-4 h-4" />
                Settings
              </Link>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={logout} className="flex items-center gap-2 text-red-600">
              <LogOut className="w-4 h-4" />
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default Header;