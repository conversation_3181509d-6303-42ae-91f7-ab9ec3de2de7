"use client";

import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Loader2, BarChart3 } from 'lucide-react';

interface CreateChartInputProps {
  onSubmit: (prompt: string) => Promise<void>;
  isLoading?: boolean;
  className?: string;
  placeholder?: string;
}

const CreateChartInput: React.FC<CreateChartInputProps> = ({
  onSubmit,
  isLoading = false,
  className = '',
  placeholder = "Describe your chart...",
}) => {
  const [prompt, setPrompt] = useState('');

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    if (!prompt.trim()) return;

    try {
      await onSubmit(prompt.trim());
      setPrompt('');
    } catch (err) {
      console.error('Failed to create chart:', err);
    }
  }, [prompt, onSubmit]);

  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  }, [handleSubmit]);

  return (
    <div className={`w-full h-full flex flex-col items-center justify-center ${className}`}>
      {/* Content */}
      <div className="text-center space-y-8 max-w-md w-full px-6">
        <div className="space-y-4">
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <BarChart3 className="h-8 w-8 text-muted-foreground" />
          </div>
          <h2 className="text-2xl font-semibold text-foreground">
            Describe your chart
          </h2>
          <p className="text-muted-foreground text-sm">
            Tell us what data you want to visualize and we'll create it for you
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="relative">
            <Input
              value={prompt}
              onChange={(e) => setPrompt(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              disabled={isLoading}
              className="w-full"
              maxLength={500}
            />
          </div>

          {prompt.trim() && (
            <Button
              type="submit"
              disabled={isLoading}
              className="w-full"
              size="lg"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Generate Chart'
              )}
            </Button>
          )}
        </form>
      </div>
    </div>
  );
};

export default CreateChartInput;
