"use client";
import React, { createContext, useContext, useState, ReactNode } from 'react';

interface PageHeaderInfo {
  title: string;
  subtitle?: string;
  icon?: React.ComponentType<{ className?: string }>;
}

interface PageHeaderContextType {
  pageInfo: PageHeaderInfo;
  setPageHeader: (info: PageHeaderInfo) => void;
  resetPageHeader: () => void;
}

const defaultPageInfo: PageHeaderInfo = {
  title: 'Agent Platform',
  subtitle: 'AI-Powered Data Analytics',
};

const PageHeaderContext = createContext<PageHeaderContextType | undefined>(undefined);

interface PageHeaderProviderProps {
  children: ReactNode;
}

export const PageHeaderProvider: React.FC<PageHeaderProviderProps> = ({ children }) => {
  const [pageInfo, setPageInfo] = useState<PageHeaderInfo>(defaultPageInfo);

  const setPageHeader = (info: PageHeaderInfo) => {
    setPageInfo(info);
  };

  const resetPageHeader = () => {
    setPageInfo(defaultPageInfo);
  };

  return (
    <PageHeaderContext.Provider value={{ pageInfo, setPageHeader, resetPageHeader }}>
      {children}
    </PageHeaderContext.Provider>
  );
};

export const usePageHeader = (): PageHeaderContextType => {
  const context = useContext(PageHeaderContext);
  if (context === undefined) {
    throw new Error('usePageHeader must be used within a PageHeaderProvider');
  }
  return context;
}; 