"use client";

import React, { useState, useCallback, useMemo } from 'react';
import { Responsive, WidthProvider } from 'react-grid-layout';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';

import { Plus, LayoutDashboard, TrendingUp } from 'lucide-react';
import { ChartWidget as ChartWidgetType, CHART_CONFIG } from '@/types';
import ChartWidget from './ChartWidget';

// Import CSS for react-grid-layout
import 'react-grid-layout/css/styles.css';
import 'react-resizable/css/styles.css';

const ResponsiveGridLayout = WidthProvider(Responsive);

const Dashboard: React.FC = () => {
  const [widgets, setWidgets] = useState<ChartWidgetType[]>([]);
  const [nextWidgetId, setNextWidgetId] = useState(1);

  // Generate layout from widgets
  const layouts = useMemo(() => {
    const layout = widgets.map((widget) => ({
      i: widget.id,
      x: widget.layout.x,
      y: widget.layout.y,
      w: widget.layout.w,
      h: widget.layout.h,
      minW: CHART_CONFIG.MIN_WIDGET_SIZE.w,
      minH: CHART_CONFIG.MIN_WIDGET_SIZE.h,
    }));

    return {
      lg: layout,
      md: layout,
      sm: layout.map(item => ({ ...item, w: Math.min(item.w, 6) })),
      xs: layout.map(item => ({ ...item, w: Math.min(item.w, 4) })),
      xxs: layout.map(item => ({ ...item, w: 2 })),
    };
  }, [widgets]);

  const createNewWidget = useCallback(() => {
    if (widgets.length >= CHART_CONFIG.MAX_WIDGETS) {
      alert(`Maximum of ${CHART_CONFIG.MAX_WIDGETS} widgets allowed`);
      return;
    }

    // Find next available position
    let x = 0;
    let y = 0;

    // Simple positioning logic - place widgets in rows
    const widgetsPerRow = Math.floor(12 / CHART_CONFIG.DEFAULT_WIDGET_SIZE.w);
    const currentRow = Math.floor(widgets.length / widgetsPerRow);
    const currentCol = widgets.length % widgetsPerRow;

    x = currentCol * CHART_CONFIG.DEFAULT_WIDGET_SIZE.w;
    y = currentRow * CHART_CONFIG.DEFAULT_WIDGET_SIZE.h;

    const newWidget: ChartWidgetType = {
      id: `widget-${nextWidgetId}`,
      title: `Chart ${nextWidgetId}`,
      chartData: null,
      isLoading: false,
      error: null,
      layout: {
        x,
        y,
        w: CHART_CONFIG.DEFAULT_WIDGET_SIZE.w,
        h: CHART_CONFIG.DEFAULT_WIDGET_SIZE.h,
      },
    };

    setWidgets(prev => [...prev, newWidget]);
    setNextWidgetId(prev => prev + 1);
  }, [widgets, nextWidgetId]);

  const deleteWidget = useCallback((widgetId: string) => {
    setWidgets(prev => prev.filter(w => w.id !== widgetId));
  }, []);

  const updateWidget = useCallback((widgetId: string, updates: Partial<ChartWidgetType>) => {
    setWidgets(prev => prev.map(w =>
      w.id === widgetId ? { ...w, ...updates } : w
    ));
  }, []);

  const handleLayoutChange = useCallback((layout: any[]) => {
    // Update widget positions based on grid layout changes
    setWidgets(prev => prev.map(widget => {
      const layoutItem = layout.find(item => item.i === widget.id);
      if (layoutItem) {
        return {
          ...widget,
          layout: {
            ...widget.layout,
            x: layoutItem.x,
            y: layoutItem.y,
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return widget;
    }));
  }, []);

  return (
    <div className="min-h-screen bg-sidebar-bg">
      <div className="container mx-auto p-6 space-y-6">
        {/* Dashboard Header */}
        <div className="flex items-center justify-between">
          <div>
            
            
          </div>

          <Button
            onClick={createNewWidget}
            disabled={widgets.length >= CHART_CONFIG.MAX_WIDGETS}
            className="rounded-lg"
          >
            <Plus className="h-4 w-4 mr-2" />
            Create Chart
          </Button>
        </div>

        {/* Stats Cards */}
        {widgets.length > 0 && (
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-muted-foreground text-sm font-medium">Total Charts</span>
                  <TrendingUp className="h-4 w-4 text-primary" />
                </div>
                <div className="text-3xl font-bold text-foreground">{widgets.length}</div>
                <p className="text-muted-foreground text-xs mt-1">
                  {CHART_CONFIG.MAX_WIDGETS - widgets.length} remaining
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-muted-foreground text-sm font-medium">Active Charts</span>
                  <TrendingUp className="h-4 w-4 text-green-600 dark:text-green-400" />
                </div>
                <div className="text-3xl font-bold text-foreground">
                  {widgets.filter(w => w.chartData && !w.error).length}
                </div>
                <p className="text-muted-foreground text-xs mt-1">
                  Successfully generated
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-muted-foreground text-sm font-medium">Generating</span>
                  <TrendingUp className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                </div>
                <div className="text-3xl font-bold text-foreground">
                  {widgets.filter(w => w.isLoading).length}
                </div>
                <p className="text-muted-foreground text-xs mt-1">
                  In progress
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Chart Widgets Grid */}
        {widgets.length > 0 && (
          <div className="relative">
            <ResponsiveGridLayout
              className="layout"
              layouts={layouts}
              breakpoints={CHART_CONFIG.BREAKPOINTS}
              cols={CHART_CONFIG.GRID_COLS}
              rowHeight={CHART_CONFIG.ROW_HEIGHT}
              onLayoutChange={handleLayoutChange}
              isDraggable={true}
              isResizable={true}
              margin={[16, 16]}
              containerPadding={[0, 0]}
            >
              {widgets.map((widget) => (
                <div key={widget.id} className="grid-item">
                  <ChartWidget
                    widget={widget}
                    onDelete={deleteWidget}
                    onUpdate={updateWidget}
                    className="h-full"
                  />
                </div>
              ))}
            </ResponsiveGridLayout>
          </div>
        )}
      </div>
    </div>
  );
};

export default Dashboard;