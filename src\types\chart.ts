// Chart-related TypeScript interfaces

export type ChartType = 'bar' | 'line' | 'pie' | 'area';

export interface ChartDataPoint {
  label: string;
  value: number;
  category?: string;
}

export interface ChartMetadata {
  xAxisLabel?: string;
  yAxisLabel?: string;
  colors?: string[];
}

export interface ChartData {
  title: string;
  chartType: ChartType;
  data: ChartDataPoint[];
  metadata: ChartMetadata;
}

export interface ChartQueryRequest {
  prompt: string;
  user_id?: string;
}

export interface ChartQueryResponse {
  success: boolean;
  data: ChartData;
  error?: string;
}

export interface ChartWidget {
  id: string;
  title: string;
  chartData: ChartData | null;
  isLoading: boolean;
  error: string | null;
  layout: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
}

export interface DashboardLayout {
  widgets: ChartWidget[];
  gridCols: number;
  gridRows: number;
}

// Grid layout types for react-grid-layout
export interface GridLayoutItem {
  i: string;
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  static?: boolean;
  isDraggable?: boolean;
  isResizable?: boolean;
}

export interface GridLayoutBreakpoint {
  lg: GridLayoutItem[];
  md: GridLayoutItem[];
  sm: GridLayoutItem[];
  xs: GridLayoutItem[];
  xxs: GridLayoutItem[];
}

// Chart configuration constants
export const CHART_CONFIG = {
  DEFAULT_WIDGET_SIZE: { w: 4, h: 3 },
  MIN_WIDGET_SIZE: { w: 2, h: 2 },
  MAX_WIDGETS: 12,
  GRID_COLS: { lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 },
  BREAKPOINTS: { lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 },
  ROW_HEIGHT: 60,
} as const;

export const DEFAULT_CHART_COLORS = [
  '#8b5cf6', // Purple
  '#06b6d4', // Cyan
  '#10b981', // Emerald
  '#f59e0b', // Amber
  '#ef4444', // Red
  '#8b5cf6', // Purple variant
  '#06b6d4', // Cyan variant
  '#10b981', // Emerald variant
  '#f59e0b', // Amber variant
  '#ef4444', // Red variant
] as const;
